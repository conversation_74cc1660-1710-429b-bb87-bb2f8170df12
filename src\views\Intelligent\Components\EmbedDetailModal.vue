<template>
  <a-modal
    v-model:open="modalVisible"
    title="嵌入详情"
    :width="900"
    :footer="null"
    class="embed-detail-modal"
    @cancel="handleClose"
  >
    <div class="modal-content">
      <!-- 服务条款说明 -->
      <div class="service-terms">
        使用本功能即表示您同意嵌入代码的相关条款。请确保您有权在目标网站上嵌入此代码，并遵守相关的服务条款、用户协议和隐私政策。
        <a class="link" @click="handleAgreementClick('service')">《服务条款》</a>
        <a class="link" @click="handleAgreementClick('user')">《用户协议》</a>
        <a class="link" @click="handleAgreementClick('privacy')">《隐私条款》</a>
        等条款约束。
      </div>

      <!-- 确认嵌入网站信息 -->
      <div class="section">
        <div class="section-header">
          <span class="section-number">1</span>
          <span class="section-title">确认嵌入网站信息</span>
        </div>
        <div class="website-info-card">
          <div class="website-info">
            <div class="website-name">{{ props.websiteInfo.website_name }}</div>
            <div class="website-domain">已配置 {{ props.websiteInfo.domain.split(',').length }} 个域名</div>
          </div>
          <a-button type="text" size="small" class="edit-btn" @click="handleEditWebsite">
            <template #icon>
              <EditOutlined />
            </template>
          </a-button>
        </div>
      </div>

      <!-- 选择嵌入类型 -->
      <div class="section">
        <div class="section-header">
          <span class="section-number">2</span>
          <span class="section-title">获取嵌入代码</span>
          <span class="section-subtitle">
            如果代码意外泄露，请及时重新生成并再次嵌入网站
          </span>
        </div>


        <!-- 类型选择器 -->
        <div class="embed-type-selection">
          <div
            class="embed-type-option"
            :class="{ active: selectedEmbedType === 'fullpage' }"
            @click="selectedEmbedType = 'fullpage'"
          >
            <img src="@/assets/image/base/pictures/embed-fullpage.png" alt="">
          </div>

          <div
            class="embed-type-option"
            :class="{ active: selectedEmbedType === 'chatbubble' }"
            @click="selectedEmbedType = 'chatbubble'"
          >
            <img src="@/assets/image/base/pictures/embed-chatbubble.png" alt="">
          </div>
        </div>
      </div>

      <!-- 代码展示区域 -->
      <div class="code-section">
        <div class="code-header">
          <span class="code-title">复制以下 JavaScript 嵌入代码到您的网站</span>
          <a-button type="text" size="small" class="copy-btn" @click="handleCopyCode">
            <template #icon>
              <CopyOutlined />
            </template>
          </a-button>
        </div>
        <div class="code-container">
          <pre class="code-content">{{ generatedCode }}</pre>
        </div>
      </div>
    </div>
  </a-modal>

  <!-- 协议详情弹窗 -->
  <a-modal
    v-model:open="agreementModalVisible"
    :title="agreementModalTitle"
    :width="800"
    :footer="null"
    class="agreement-modal"
    @cancel="handleAgreementClose"
  >
    <div class="agreement-content">
      <div class="agreement-text">{{ agreementContent }}</div>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { EditOutlined, CopyOutlined } from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';

// 定义 props
interface Props {
  visible: boolean;
  websiteInfo: {
    website_name: string;
    domain: string;
    agentUrl?: string;
  };
}

const props = defineProps<Props>();

// 定义 emits
const emit = defineEmits<{
  'update:visible': [value: boolean];
  close: [];
  'edit-website': [];
}>();

// 响应式变量
const modalVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
});

// 选中的嵌入类型
const selectedEmbedType = ref<'fullpage' | 'chatbubble'>('fullpage');

// 协议弹窗相关
const agreementModalVisible = ref(false);
const agreementModalTitle = ref('');
const agreementContent = ref('');

// 协议内容数据
const agreementData = {
  service: {
    title: '服务条款',
    content: `一、服务条款

1. 服务说明
本服务条款是您与我们之间关于使用本平台服务的法律协议。通过使用本服务，您同意遵守本条款的所有规定。

2. 服务内容
我们提供 AI 智能对话、嵌入代码生成等相关服务。服务内容可能会根据业务发展需要进行调整。

3. 用户义务
- 您应当合法使用本服务，不得用于任何违法违规目的
- 您应当保护好自己的账户信息，对账户下的所有活动负责
- 您不得恶意攻击、破坏本服务的正常运行

4. 服务限制
- 我们有权根据实际情况对服务进行维护、升级或暂停
- 对于免费服务，我们不承诺服务的持续性和稳定性

5. 知识产权
本服务中的所有内容，包括但不限于文字、图片、音频、视频、软件等，均受知识产权法保护。

6. 免责声明
在法律允许的最大范围内，我们对因使用本服务而产生的任何直接或间接损失不承担责任。

7. 条款变更
我们有权随时修改本服务条款，修改后的条款将在平台上公布。继续使用服务即表示您接受修改后的条款。`
  },
  user: {
    title: '用户协议',
    content: `一、用户协议

1. 协议范围
本用户协议适用于所有使用本平台服务的用户。注册或使用本服务即表示您同意本协议的全部内容。

2. 账户注册
- 您应当提供真实、准确的注册信息
- 您应当及时更新注册信息，确保信息的有效性
- 一个手机号码或邮箱只能注册一个账户

3. 账户安全
- 您应当妥善保管账户密码，不得向他人透露
- 如发现账户被盗用，应立即通知我们
- 您对账户下的所有操作承担责任

4. 使用规范
- 不得发布违法、有害、威胁、辱骂、骚扰、侵权的内容
- 不得传播垃圾信息、广告信息或恶意软件
- 不得干扰或破坏服务的正常运行

5. 内容权利
- 您对自己发布的内容拥有知识产权
- 您授权我们在提供服务过程中使用您的内容
- 我们有权删除违规内容

6. 服务变更
我们有权随时修改、暂停或终止服务，并会提前通知用户。

7. 违约处理
如您违反本协议，我们有权采取警告、限制功能、暂停或终止服务等措施。

8. 争议解决
因本协议产生的争议，双方应友好协商解决；协商不成的，提交有管辖权的人民法院解决。`
  },
  privacy: {
    title: '隐私条款',
    content: `一、隐私条款

1. 隐私保护承诺
我们高度重视用户隐私保护，本隐私条款说明我们如何收集、使用、存储和保护您的个人信息。

2. 信息收集
我们可能收集以下信息：
- 注册信息：用户名、邮箱、手机号码等
- 使用信息：登录记录、操作日志、设备信息等
- 内容信息：您在使用服务过程中产生的对话内容、上传的文件等

3. 信息使用
我们收集信息的目的：
- 提供和改进服务
- 用户身份验证和账户安全
- 客户服务和技术支持
- 产品分析和优化

4. 信息共享
我们不会向第三方出售、出租或交易您的个人信息，除非：
- 获得您的明确同意
- 法律法规要求
- 保护我们的合法权益

5. 信息存储
- 我们采用行业标准的安全措施保护您的信息
- 信息存储在安全的服务器中，采用加密技术
- 我们会定期审查和更新安全措施

6. 信息访问和控制
您有权：
- 访问和更新您的个人信息
- 删除您的账户和相关信息
- 选择接收或拒绝营销信息

7. Cookie 使用
我们使用 Cookie 来改善用户体验，您可以通过浏览器设置控制 Cookie 的使用。

8. 未成年人保护
我们不会故意收集未满 18 岁用户的个人信息。如发现，我们会立即删除相关信息。

9. 隐私条款更新
我们可能会更新本隐私条款，更新后会在平台上公布。继续使用服务即表示您接受更新后的条款。

10. 联系我们
如您对隐私保护有任何问题，请通过平台客服联系我们。`
  }
};

// 生成的代码 - 使用模板字符串避免 Vue 编译器冲突
const generatedCode = computed(() => {
  const scriptTag = 'script';
  return `<!-- 以下代码嵌入到前端 html 文件的 body 内 -->
<${scriptTag} src="https://minio-prod.shukeyun.com/ai-prod-models/frontend/ai-chat-sdk.js"></${scriptTag}>
<${scriptTag}>
      window.AIChatConfig = {
        iframeUrl: '${props.websiteInfo.agentUrl}',
      };
</${scriptTag}>`;
});

// 关闭弹窗
const handleClose = () => {
  emit('close');
};

// 复制代码
const handleCopyCode = async () => {
  try {
    await navigator.clipboard.writeText(generatedCode.value);
    message.success('代码已复制到剪贴板');
  } catch (error) {
    message.error('复制失败，请手动复制');
  }
};

// 编辑网站信息
const handleEditWebsite = () => {
  emit('edit-website');
};

// 处理协议链接点击
const handleAgreementClick = (type: 'service' | 'user' | 'privacy') => {
  const agreement = agreementData[type];
  agreementModalTitle.value = agreement.title;
  agreementContent.value = agreement.content;
  agreementModalVisible.value = true;
};

// 关闭协议弹窗
const handleAgreementClose = () => {
  agreementModalVisible.value = false;
};
</script>

<style scoped>
.embed-detail-modal :deep(.ant-modal-header) {
  border-bottom: 1px solid #f0f0f0;
  padding: 16px 24px;
}

.embed-detail-modal :deep(.ant-modal-body) {
  padding: 24px;
}

.modal-content {
  font-size: 14px;
  line-height: 1.5;
}

/* 服务条款说明 */
.service-terms {
  color: #666;
  line-height: 1.6;
  margin-bottom: 24px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
}

.service-terms .link {
  color: #1890ff;
  text-decoration: none;
  margin: 0 2px;
}

.service-terms .link:hover {
  text-decoration: underline;
}

/* 章节样式 */
.section {
  margin-bottom: 32px;
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.section-number {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  background: #1890ff;
  color: white;
  border-radius: 50%;
  font-size: 12px;
  font-weight: 500;
  margin-right: 8px;
}

.section-title {
  font-size: 16px;
  font-weight: 500;
  color: #262626;
}

.section-subtitle {
  margin-left: 10px;
  color: #666;
  font-size: 12px;
}

/* 网站信息卡片 */
.website-info-card {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  background: #fafafa;
}

.website-info {
  flex: 1;
}

.website-name {
  font-size: 14px;
  font-weight: 500;
  color: #262626;
  margin-bottom: 4px;
}

.website-domain {
  font-size: 12px;
  color: #8c8c8c;
}

.edit-btn {
  color: #666;
  padding: 4px;
}

.edit-btn:hover {
  color: #1890ff;
}

/* 嵌入类型选择 */
.embed-type-selection {
  display: flex;
  gap: 20px;
}

.embed-type-option {
  border: 2px solid transparent;
  border-radius: 15px;
  cursor: pointer;
  transition: all 0.3s;
}
.embed-type-option img {
  width: 300px;
}

.embed-type-option:hover {
  border-color: #1890ff;
}

.embed-type-option.active {
  border-color: #1890ff;
  background: #f6f9ff;
}

/* 代码展示区域 */
.code-section {
  border-top: 1px solid #f0f0f0;
  padding-top: 24px;
}

.code-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: #4a5568;
  border-radius: 6px 6px 0 0;
  margin-bottom: 0;
}

.code-title {
  font-size: 14px;
  font-weight: 400;
  color: #e2e8f0;
}

.copy-btn {
  height: 24px;
  width: 24px;
  padding: 0;
  color: #e2e8f0;
  border: none;
  background: transparent;
  display: flex;
  align-items: center;
  justify-content: center;
}

.copy-btn:hover {
  color: #ffffff;
  background: rgba(255, 255, 255, 0.1);
}

.code-container {
  background: #2d3748;
  border-radius: 0 0 6px 6px;
  padding: 16px;
  overflow-x: auto;
  border-top: 1px solid #4a5568;
}

.code-content {
  color: #e2e8f0;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.5;
  margin: 0;
  white-space: pre-wrap;
  word-break: break-all;
}

/* 协议弹窗样式 */
.agreement-modal :deep(.ant-modal-header) {
  border-bottom: 1px solid #f0f0f0;
  padding: 16px 24px;
}

.agreement-modal :deep(.ant-modal-body) {
  padding: 24px;
  max-height: 60vh;
  overflow-y: auto;
}

.agreement-content {
  font-size: 14px;
  line-height: 1.6;
}

.agreement-text {
  color: #262626;
  white-space: pre-line;
}
</style>